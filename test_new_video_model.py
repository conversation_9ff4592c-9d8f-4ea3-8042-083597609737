#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的视频模型API
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import MODELS, generate_video_wan2_2_t2v

def test_new_video_model():
    """测试新的视频模型配置"""
    print("=== 测试新的视频模型配置 ===")
    
    # 查找新的视频模型
    new_model = None
    new_model_index = None
    
    for i, model in enumerate(MODELS):
        if model.get('api_type') == 'wan2_2_t2v':
            new_model = model
            new_model_index = i
            break
    
    if new_model:
        print(f"✅ 找到新的视频模型:")
        print(f"   - 索引: {new_model_index}")
        print(f"   - 名称: {new_model['display_name']}")
        print(f"   - 源地址: {new_model['source_url']}")
        print(f"   - API类型: {new_model.get('api_type', 'default')}")
        print(f"   - 类型: {new_model['type']}")
        
        # 测试API数据格式
        print("\n=== 测试API数据格式 ===")
        test_image_url = "https://raw.githubusercontent.com/gradio-app/gradio/main/test/test_files/bus.png"
        test_prompt = "make this image come alive, cinematic motion"
        test_negative_prompt = "static, blurred"
        test_cfg = 1
        test_steps = 5
        test_seed = 0
        test_enable_random_seed = True
        
        print(f"测试参数:")
        print(f"  - 图像URL: {test_image_url}")
        print(f"  - 提示词: {test_prompt}")
        print(f"  - 反向提示词: {test_negative_prompt}")
        print(f"  - CFG: {test_cfg}")
        print(f"  - 步数: {test_steps}")
        print(f"  - 种子: {test_seed}")
        print(f"  - 启用随机种子: {test_enable_random_seed}")
        
        # 构建预期的API数据格式
        expected_data = {
            "data": [
                {
                    "path": test_image_url,
                    "meta": {"_type": "gradio.FileData"}
                },
                test_prompt,
                test_negative_prompt,
                3,  # 固定为3
                test_cfg,  # cfg参数
                test_steps,  # 步数
                test_seed,  # 种子
                test_enable_random_seed  # 是否启用随机种子
            ]
        }
        
        print(f"\n预期的API数据格式:")
        import json
        print(json.dumps(expected_data, indent=2, ensure_ascii=False))
        
        print("\n✅ 新视频模型配置测试通过！")
        return True
    else:
        print("❌ 未找到新的视频模型")
        return False

def test_model_selection():
    """测试模型选择逻辑"""
    print("\n=== 测试模型选择逻辑 ===")
    
    # 统计视频模型
    video_models = [model for model in MODELS if model['type'] == 'video']
    wan2_2_t2v_models = [model for model in MODELS if model.get('api_type') == 'wan2_2_t2v']
    
    print(f"总共有 {len(video_models)} 个视频模型")
    print(f"其中 {len(wan2_2_t2v_models)} 个是wan2-2-t2v类型")
    
    print("\n所有视频模型:")
    for i, model in enumerate(video_models):
        api_type = model.get('api_type', 'default')
        model_index = MODELS.index(model)
        print(f"  {i+1}. [{model_index}] {model['display_name']} - API类型: {api_type}")
    
    return True

if __name__ == "__main__":
    print("开始测试新的视频模型...")
    
    try:
        # 测试模型配置
        config_ok = test_new_video_model()
        
        # 测试模型选择
        selection_ok = test_model_selection()
        
        if config_ok and selection_ok:
            print("\n🎉 所有测试通过！新的视频模型已成功添加。")
        else:
            print("\n❌ 测试失败，请检查配置。")
            
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
